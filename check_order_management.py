#!/usr/bin/env python3
"""
Script de vérification des nouvelles fonctionnalités de gestion des commandes
"""

import os
import sys

def check_file_content(file_path, patterns, description):
    """Vérifie si des patterns spécifiques existent dans un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        missing_patterns = []
        for pattern in patterns:
            if pattern not in content:
                missing_patterns.append(pattern)
        
        if missing_patterns:
            print(f"❌ {description} - Patterns manquants:")
            for pattern in missing_patterns:
                print(f"   - {pattern}")
            return False
        else:
            print(f"✅ {description}")
            return True
            
    except FileNotFoundError:
        print(f"❌ {description} - Fichier manquant: {file_path}")
        return False

def main():
    print("🔍 Vérification des fonctionnalités de gestion des commandes en cours")
    print("=" * 75)
    
    all_good = True
    
    # 1. Vérification des nouvelles modals dans service_selector.html
    print("\n📋 1. Modals de gestion des commandes")
    modal_patterns = [
        'id="currentOrderModal"',
        'id="moveTableModal"',
        'id="addItemsModal"',
        'currentOrderTableName',
        'currentOrderInfo',
        'currentCoversCount',
        'moveTableBtn',
        'addItemsBtn',
        'updateOrderBtn',
        'sendUpdatesToKitchenBtn',
        'cancelOrderBtn'
    ]
    
    if not check_file_content(
        'app/modules/pos/templates/pos/service_selector.html',
        modal_patterns,
        "Modals de gestion des commandes"
    ):
        all_good = False
    
    # 2. Vérification des nouvelles méthodes JavaScript
    print("\n📜 2. Méthodes JavaScript")
    js_patterns = [
        'showCurrentOrder',
        'displayCurrentOrder',
        'updateItemQuantity',
        'updateOrderCovers',
        'openMoveTableModal',
        'confirmMoveTable',
        'openAddItemsModal',
        'confirmAddItems',
        'sendUpdatesToKitchen',
        'cancelOrder',
        'table.status === \'occupied\''
    ]
    
    if not check_file_content(
        'app/modules/pos/static/js/service-selector.js',
        js_patterns,
        "Méthodes JavaScript de gestion"
    ):
        all_good = False
    
    # 3. Vérification des nouvelles routes API
    print("\n🛣️ 3. Routes API")
    api_patterns = [
        'get_table_order',
        'update_order_covers',
        'add_items_to_order',
        'update_order_item',
        'move_order_to_table',
        'send_order_updates_to_kitchen',
        'cancel_order',
        'get_products_and_categories'
    ]
    
    if not check_file_content(
        'app/modules/pos/routes.py',
        api_patterns,
        "Routes API de gestion des commandes"
    ):
        all_good = False
    
    # 4. Vérification des méthodes des modèles
    print("\n📊 4. Méthodes des modèles")
    
    # Vérifier Table
    table_patterns = [
        'def is_available',
        'def occupy',
        'def reset_table',
        'current_covers',
        'current_order_id'
    ]
    
    if not check_file_content(
        'app/modules/tables/models_table.py',
        table_patterns,
        "Méthodes du modèle Table"
    ):
        all_good = False
    
    # Vérifier Sale
    sale_patterns = [
        'covers_count',
        'service_type',
        'remaining_amount',
        'is_partially_paid',
        'service_type_display'
    ]
    
    if not check_file_content(
        'app/modules/pos/models_sale.py',
        sale_patterns,
        "Propriétés du modèle Sale"
    ):
        all_good = False
    
    # 5. Vérification des styles CSS
    print("\n🎨 5. Styles CSS")
    css_patterns = [
        '.table-mini.occupied:hover',
        '.product-card',
        '.room-plan-container',
        'cursor: pointer',
        'transform: scale'
    ]
    
    if not check_file_content(
        'app/modules/pos/templates/pos/service_selector.html',
        css_patterns,
        "Styles CSS pour l'interaction"
    ):
        all_good = False
    
    # 6. Vérification de l'intégration
    print("\n🔗 6. Intégration système")
    
    # Vérifier que le clic sur table occupée est géré
    integration_patterns = [
        'if (table.status === \'occupied\')',
        'this.showCurrentOrder(table)',
        'occupied:hover'
    ]
    
    if not check_file_content(
        'app/modules/pos/static/js/service-selector.js',
        integration_patterns,
        "Intégration clic sur table occupée"
    ):
        all_good = False
    
    # 7. Vérification des fichiers de documentation
    print("\n📚 7. Documentation")
    
    doc_files = [
        'ORDER_MANAGEMENT_GUIDE.md',
        'test_order_management.py',
        'SYSTEM_IMPROVEMENTS_SUMMARY.md'
    ]
    
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            print(f"✅ Documentation: {doc_file}")
        else:
            print(f"❌ Documentation manquante: {doc_file}")
            all_good = False
    
    # 8. Vérification des fonctionnalités spécifiques
    print("\n🎯 8. Fonctionnalités spécifiques")
    
    features = [
        ("Clic sur table occupée", "app/modules/pos/static/js/service-selector.js", ["table.status === 'occupied'", "showCurrentOrder"]),
        ("Modification des couverts", "app/modules/pos/static/js/service-selector.js", ["updateOrderCovers", "covers_count"]),
        ("Ajout d'articles", "app/modules/pos/static/js/service-selector.js", ["addToTempCart", "confirmAddItems"]),
        ("Déplacement de table", "app/modules/pos/static/js/service-selector.js", ["moveTableBtn", "confirmMoveTable"]),
        ("Annulation de commande", "app/modules/pos/static/js/service-selector.js", ["cancelOrder", "cancel_order"]),
        ("API produits", "app/modules/pos/routes.py", ["get_products_and_categories", "ProductCategory"])
    ]
    
    for feature_name, file_path, patterns in features:
        if check_file_content(file_path, patterns, f"Fonctionnalité: {feature_name}"):
            pass  # Déjà affiché par check_file_content
        else:
            all_good = False
    
    # Résumé final
    print("\n" + "=" * 75)
    if all_good:
        print("🎉 TOUTES LES FONCTIONNALITÉS SONT IMPLÉMENTÉES!")
        print("\n📋 Fonctionnalités disponibles:")
        print("   ✅ Clic sur table occupée pour gérer la commande")
        print("   ✅ Modification du nombre de couverts")
        print("   ✅ Ajout/suppression d'articles en cours de service")
        print("   ✅ Déplacement de commande vers une autre table")
        print("   ✅ Envoi des modifications en cuisine")
        print("   ✅ Annulation de commande avec raison")
        print("   ✅ Interface complète avec modals")
        print("   ✅ APIs pour toutes les opérations")
        print("   ✅ Styles CSS pour l'interaction")
        print("   ✅ Documentation complète")
        
        print("\n🚀 Pour tester:")
        print("1. Démarrez l'application Flask")
        print("2. Accédez au POS (/pos/)")
        print("3. Créez une commande sur une table")
        print("4. Cliquez sur la table occupée (rouge)")
        print("5. Testez toutes les fonctionnalités dans la modal")
        
        return 0
    else:
        print("⚠️ CERTAINES FONCTIONNALITÉS SONT MANQUANTES!")
        print("\n🔧 Actions requises:")
        print("- Vérifiez les fichiers signalés ci-dessus")
        print("- Assurez-vous que tous les patterns sont présents")
        print("- Relancez ce script après correction")
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
