{% extends "base.html" %}

{% block title %}Commandes Prêtes{% endblock %}

{% block extra_css %}
<style>
/* Styles pour la page des commandes prêtes */
.ready-orders-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 20px 0;
    margin: -20px -15px 30px -15px;
    border-radius: 0 0 15px 15px;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
    position: relative;
    overflow: hidden;
}

.ready-orders-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 50%, rgba(255,255,255,0.1) 50%, rgba(255,255,255,0.1) 75%, transparent 75%, transparent);
    background-size: 100px 100px;
    animation: move-bg 20s linear infinite;
    opacity: 0.1;
    pointer-events: none;
}

@keyframes move-bg {
    0% { background-position: 0 0; }
    100% { background-position: 100px 100px; }
}

.ready-orders-header h2 {
    margin: 0;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    position: relative;
}

.nav-buttons {
    display: flex;
    gap: 10px;
}

.nav-btn {
    padding: 8px 15px;
    border-radius: 8px;
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    cursor: pointer;
    pointer-events: auto;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.order-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    margin-bottom: 25px;
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.order-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.order-card-header {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 15px 20px;
    border: none;
    position: relative;
}

.order-card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.order-number {
    font-size: 1.1em;
    font-weight: 600;
    margin: 0;
}

.order-time {
    font-size: 0.9em;
    opacity: 0.9;
    margin: 0;
}

.table-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1);
    color: white;
    border-radius: 10px;
    padding: 12px 15px;
    margin: 15px;
    text-align: center;
    font-weight: 600;
    box-shadow: 0 3px 10px rgba(23, 162, 184, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.items-list {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin: 15px;
    max-height: 200px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #28a745 #f8f9fa;
}

.items-list::-webkit-scrollbar {
    width: 6px;
}

.items-list::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.items-list::-webkit-scrollbar-thumb {
    background-color: #28a745;
    border-radius: 3px;
}

.item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.item-row:hover {
    background: rgba(40, 167, 69, 0.05);
    padding-left: 5px;
}

.item-row:last-child {
    border-bottom: none;
}

.item-name {
    font-weight: 500;
    color: #495057;
}

.item-quantity {
    background: #007bff;
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.85em;
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.kitchen-note {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 2px solid #ffc107;
    border-radius: 10px;
    padding: 15px;
    margin: 15px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

.kitchen-note-title {
    color: #856404;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.kitchen-note-text {
    color: #856404;
    margin: 0;
    font-style: italic;
}

.total-section {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    margin: 15px;
    box-shadow: 0 3px 10px rgba(40, 167, 69, 0.3);
}

.total-amount {
    font-size: 1.5em;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.action-buttons {
    display: grid;
    gap: 10px;
    padding: 20px;
    background: #f8f9fa;
}

.btn-pay-cash, .btn-pay-other, .btn-delivered {
    padding: 12px 20px;
    border-radius: 10px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-pay-cash {
    background: linear-gradient(135deg, #28a745, #20c997);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.btn-pay-other {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-delivered {
    background: linear-gradient(135deg, #6c757d, #495057);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-pay-cash:hover, .btn-pay-other:hover, .btn-delivered:hover {
    transform: translateY(-2px);
    color: white;
    box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

.btn-pay-cash:hover { background: linear-gradient(135deg, #218838, #1ea080); }
.btn-pay-other:hover { background: linear-gradient(135deg, #e0a800, #e8690b); }
.btn-delivered:hover { background: linear-gradient(135deg, #5a6268, #343a40); }

.empty-state {
    text-align: center;
    padding: 50px 20px;
    background: #f8f9fa;
    border-radius: 15px;
    margin-top: 30px;
    animation: fadeIn 0.5s ease-out;
}

.empty-state-icon {
    font-size: 4em;
    color: #6c757d;
    margin-bottom: 20px;
}

.empty-state-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 10px;
}

.empty-state-text {
    color: #6c757d;
    margin-bottom: 30px;
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out, fadeOut 0.3s ease-out 2.7s forwards;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.notification-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.notification-error {
    background: linear-gradient(135deg, #dc3545, #c82333);
}

@keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

/* Refresh indicator */
.refresh-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(40, 167, 69, 0.9);
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    z-index: 1000;
    animation: fadeIn 0.3s ease-out;
}

.refresh-indicator i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête amélioré -->
    <div class="ready-orders-header">
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center">
                <h2>
                    <i class="fas fa-bell me-2"></i>
                    Commandes Prêtes à Servir
                    {% if orders %}
                        <span class="badge bg-light text-dark ms-2">{{ orders|length }}</span>
                    {% endif %}
                </h2>
                <div class="nav-buttons">
                    <a href="{{ url_for('pos.kitchen_orders') }}" class="nav-btn">
                        <i class="fas fa-utensils"></i> Cuisine
                    </a>
                    <a href="{{ url_for('pos.index') }}" class="nav-btn">
                        <i class="fas fa-cash-register"></i> POS
                    </a>
                </div>
            </div>
        </div>
    </div>

    {% if orders %}
    <div class="row">
        {% for order in orders %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card order-card h-100">
                <!-- En-tête de la commande -->
                <div class="order-card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="order-number">
                            <i class="fas fa-receipt me-2"></i>
                            Commande #{{ order.reference }}
                        </h6>
                        <small class="order-time">
                            <i class="fas fa-clock me-1"></i>
                            {{ order.created_at.strftime('%H:%M') }}
                        </small>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- Informations de table -->
                    {% if order.table %}
                    <div class="table-info">
                        <i class="fas fa-table me-2"></i>
                        <strong>Table {{ order.table.number }}</strong>
                        {% if order.table.location %}
                            <small class="d-block mt-1">{{ order.table.location }}</small>
                        {% endif %}
                    </div>
                    {% endif %}

                    <!-- Articles de la commande -->
                    <div class="items-list">
                        <h6 class="text-muted mb-3">
                            <i class="fas fa-list me-2"></i>Articles ({{ order.items.count() }})
                        </h6>
                        {% for item in order.items %}
                        <div class="item-row">
                            <span class="item-name">{{ item.product.name }}</span>
                            <span class="item-quantity">{{ item.quantity|int }}</span>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Note de cuisine -->
                    {% if order.kitchen_note %}
                    <div class="kitchen-note">
                        <div class="kitchen-note-title">
                            <i class="fas fa-sticky-note"></i>
                            Note pour la cuisine
                        </div>
                        <p class="kitchen-note-text">{{ order.kitchen_note }}</p>
                    </div>
                    {% endif %}

                    <!-- Total -->
                    <div class="total-section">
                        <p class="total-amount">
                            <i class="fas fa-euro-sign me-2"></i>{{ "%.2f"|format(order.total) }} €
                        </p>
                    </div>
                </div>

                <!-- Boutons d'action -->
                <div class="action-buttons">
                    <!-- Formulaire de paiement direct -->
                    <form action="{{ url_for('pos.process_payment') }}" method="POST" style="display: inline;">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <input type="hidden" name="sale_id" value="{{ order.id }}">
                        <input type="hidden" name="payment_method" value="CASH">
                        <input type="hidden" name="amount_tendered" value="{{ order.total }}">
                        <button type="submit" class="btn btn-pay-cash w-100">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            Encaisser en Espèces
                        </button>
                    </form>

                    <button type="button" class="btn btn-pay-other w-100" onclick="openPaymentModal({{ order.id }})">
                        <i class="fas fa-credit-card me-2"></i>
                        Autre Méthode de Paiement
                    </button>

                    <button type="button" class="btn btn-delivered w-100" onclick="markOrderAsDelivered({{ order.id }})">
                        <i class="fas fa-check-circle me-2"></i>
                        Marquer comme Servi
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <!-- État vide amélioré -->
    <div class="empty-state">
        <div class="empty-state-icon">
            <i class="fas fa-bell-slash"></i>
        </div>
        <h4 class="empty-state-title">Aucune commande prête</h4>
        <p class="empty-state-text">
            Toutes les commandes ont été servies ou sont encore en préparation en cuisine.
        </p>
        <div class="d-flex justify-content-center gap-3">
            <a href="{{ url_for('pos.kitchen_orders') }}" class="nav-btn">
                <i class="fas fa-utensils"></i> Voir la Cuisine
            </a>
            <a href="{{ url_for('pos.index') }}" class="nav-btn">
                <i class="fas fa-cash-register"></i> Retour au POS
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Indicateur de rafraîchissement -->
<div class="refresh-indicator" id="refreshIndicator" style="display: none;">
    <i class="fas fa-sync-alt"></i>
    <span>Rafraîchissement dans <span id="refreshCountdown">30</span>s</span>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Fonction pour afficher les notifications
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Fonction pour marquer une commande comme servie
function markOrderAsDelivered(orderId) {
    if (confirm('🍽️ Marquer cette commande comme servie ?\n\nCette action indique que la commande a été livrée au client.')) {
        const button = document.querySelector(`button[onclick="markOrderAsDelivered(${orderId})"]`);
        const originalText = button.innerHTML;

        // Désactiver le bouton et afficher un spinner
        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Traitement...';

        fetch(`/pos/kitchen/order/${orderId}/delivered`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').content
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Commande marquée comme servie avec succès !');
                // Animation de succès
                button.innerHTML = '<i class="fas fa-check me-2"></i>Servi !';
                button.className = 'btn btn-success w-100';

                // Rediriger vers la page des ventes après un court délai
                setTimeout(() => {
                    window.location.href = "{{ url_for('pos.sales') }}";
                }, 1000);
            } else {
                showNotification(data.error, 'error');
                button.disabled = false;
                button.innerHTML = originalText;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Erreur de connexion', 'error');
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }
}

// Gestion du rafraîchissement automatique
let refreshInterval;
let refreshCountdown = 30;
const refreshIndicator = document.getElementById('refreshIndicator');
const countdownElement = document.getElementById('refreshCountdown');

function startRefreshCountdown() {
    refreshIndicator.style.display = 'flex';
    refreshCountdown = 30;
    countdownElement.textContent = refreshCountdown;

    if (refreshInterval) {
        clearInterval(refreshInterval);
    }

    refreshInterval = setInterval(() => {
        refreshCountdown--;
        countdownElement.textContent = refreshCountdown;

        if (refreshCountdown <= 0) {
            location.reload();
        }
    }, 1000);
}

// Démarrer le compte à rebours au chargement de la page
document.addEventListener('DOMContentLoaded', () => {
    startRefreshCountdown();
});

// Réinitialiser le compte à rebours lors des interactions utilisateur
document.addEventListener('click', () => {
    startRefreshCountdown();
});

// Fonction pour ouvrir le modal de paiement
function openPaymentModal(orderId) {
    // Implémenter l'ouverture du modal de paiement ici
    showNotification('Fonctionnalité de paiement en cours de développement', 'error');
}
</script>
{% endblock %}
