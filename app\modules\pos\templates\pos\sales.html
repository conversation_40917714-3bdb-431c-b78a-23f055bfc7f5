{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-shopping-cart"></i> {{ title }}
        </h2>
        <a href="{{ url_for('pos.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour au POS
        </a>
    </div>

    <!-- Filtres -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">Statut</label>
                    <select name="status" class="form-select">
                        <option value="">Tous</option>
                        <option value="pending" {% if status == 'pending' %}selected{% endif %}>En attente</option>
                        <option value="paid" {% if status == 'paid' %}selected{% endif %}>Payée</option>
                        <option value="cancelled" {% if status == 'cancelled' %}selected{% endif %}>Annulée</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date début</label>
                    <input type="date" name="date_from" class="form-control" value="{{ date_from.strftime('%Y-%m-%d') if date_from }}">
                </div>
                <div class="col-md-3">
                    <label class="form-label">Date fin</label>
                    <input type="date" name="date_to" class="form-control" value="{{ date_to.strftime('%Y-%m-%d') if date_to }}">
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter"></i> Filtrer
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des ventes -->
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Client</th>
                            <th>Table</th>
                            <th>Total</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sale in sales.items %}
                        <tr>
                            <td>{{ sale.reference }}</td>
                            <td>{{ sale.created_at.strftime('%d/%m/%Y %H:%M') }}</td>
                            <td>
                                {% if sale.customer %}
                                {{ sale.customer.first_name }} {{ sale.customer.last_name }}
                                {% else %}
                                Client anonyme
                                {% endif %}
                            </td>
                            <td>
                                {% if sale.table %}
                                    Table {{ sale.table.number }}
                                    {% if sale.table.location %}({{ sale.table.location }}){% endif %}
                                {% else %}
                                    Non attribuée
                                {% endif %}
                            </td>
                            <td>{{ "%.2f"|format(sale.total) }} €</td>
                            <td>
                                {% if sale.status.value == 'pending' %}
                                <span class="badge bg-warning">En attente</span>
                                {% elif sale.status.value == 'paid' %}
                                <span class="badge bg-success">Payée</span>
                                {% elif sale.status.value == 'cancelled' %}
                                <span class="badge bg-danger">Annulée</span>
                                {% endif %}
                            </td>
                            <td>
                                <a href="{{ url_for('pos.sale_details', id=sale.id) }}"
                                   class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="6" class="text-center">Aucune vente trouvée</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if sales.pages > 1 %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% for page in range(1, sales.pages + 1) %}
                    <li class="page-item {% if page == sales.page %}active{% endif %}">
                        <a class="page-link" href="{{ url_for('pos.sales', page=page, status=status, date_from=date_from.strftime('%Y-%m-%d') if date_from, date_to=date_to.strftime('%Y-%m-%d') if date_to) }}">
                            {{ page }}
                        </a>
                    </li>
                    {% endfor %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}