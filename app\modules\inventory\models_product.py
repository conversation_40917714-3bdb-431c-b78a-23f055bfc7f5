from app import db
from datetime import datetime
from flask_login import current_user
from app.modules.inventory.models_stock_movement import StockMovement, StockMovementType, StockMovementReason

class ProductCategory(db.Model):
    __tablename__ = 'product_categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)
    image_path = db.Column(db.String(255))
    color = db.Column(db.String(50), default='#6c757d')  # Default color for categories
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relations
    products = db.relationship('Product', backref='category', lazy='dynamic')
    owner = db.relationship('User', backref='product_categories')
    
    def __repr__(self):
        return f'<ProductCategory {self.name}>'

class Product(db.Model):
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    cost_price = db.Column(db.Float)
    image_path = db.Column(db.String(255))
    category_id = db.Column(db.Integer, db.ForeignKey('product_categories.id'), nullable=False)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    stock_quantity = db.Column(db.Float, default=0)
    minimum_stock = db.Column(db.Float, default=0)
    unit = db.Column(db.String(20), default='unité')
    has_recipe = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    variants = db.relationship('ProductVariant', backref='product', lazy='dynamic', cascade='all, delete-orphan')
    sale_items = db.relationship('SaleItem', backref='product', lazy='dynamic')
    recipe = db.relationship('Recipe', back_populates='product', uselist=False)
    owner = db.relationship('User', backref='products')
    
    def get_available_quantity(self):
        """Retourne la quantité disponible (stock direct ou calculé via recette)"""
        direct_stock = self.stock_quantity or 0
        
        if not self.has_recipe:
            return direct_stock
        
        if self.recipe:
            recipe_stock = self.recipe.get_possible_quantity()
            return recipe_stock
        
        return direct_stock
    
    def get_stock_status(self):
        """Retourne le statut du stock (out_of_stock, low_stock, in_stock)"""
        available = self.get_available_quantity()
        
        if available <= 0:
            return 'out_of_stock'
        elif available <= self.minimum_stock:
            return 'low_stock'
        return 'in_stock'
    
    def calculate_recipe_cost(self):
        """Calcule le coût de production basé sur la recette"""
        if not self.has_recipe or not hasattr(self, 'recipe') or not self.recipe:
            return 0
        return self.recipe.calculate_cost()
    
    def calculate_profit_margin(self):
        """Calcule la marge bénéficiaire en pourcentage"""
        if self.price <= 0:
            return 0
        
        cost = self.calculate_recipe_cost() if self.has_recipe else self.cost_price or 0
        if cost <= 0:
            return 100  # Si pas de coût, marge de 100%
        
        margin = ((self.price - cost) / self.price) * 100
        return max(0, margin)  # Évite les marges négatives
    
    def update_stock(self, quantity, operation='subtract', reason=None, reference=None, notes=None):
        """Met à jour le stock du produit et enregistre le mouvement"""
        if self.has_recipe:
            return False  # Les produits basés sur des recettes ne peuvent pas être mis à jour directement
        
        previous_quantity = self.stock_quantity
        
        if operation == 'add':
            self.stock_quantity += quantity
            movement_type = StockMovementType.IN
        elif operation in ['subtract', 'remove']:
            if self.stock_quantity >= quantity:
                self.stock_quantity -= quantity
                movement_type = StockMovementType.OUT
            else:
                return False
        
        # Créer le mouvement de stock
        movement = StockMovement(
            owner_id=self.owner_id,
            user_id=current_user.id,
            product_id=self.id,
            type=movement_type,
            reason=reason or StockMovementReason.ADJUSTMENT,
            quantity=quantity,
            previous_quantity=previous_quantity,
            new_quantity=self.stock_quantity,
            reference=reference,
            notes=notes
        )
        
        db.session.add(movement)
        db.session.commit()
        return True
    
    def __repr__(self):
        return f'<Product {self.name}>'

class ProductVariant(db.Model):
    __tablename__ = 'product_variants'
    
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    size = db.Column(db.String(20))  # S, M, L, XL, etc.
    color = db.Column(db.String(50))
    stock_quantity = db.Column(db.Integer, default=0)
    price_adjustment = db.Column(db.Float, default=0.0)  # Additional price for this variant
    
    def get_final_price(self):
        return self.product.price + self.price_adjustment
    
    def __repr__(self):
        return f'<ProductVariant {self.product.name} - Size: {self.size}, Color: {self.color}>' 