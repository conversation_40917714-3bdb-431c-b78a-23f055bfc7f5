#!/usr/bin/env python3
"""
Script de vérification de la complétude du système de salles et tables
"""

import os
import sys

def check_file_exists(file_path, description):
    """Vérifie si un fichier existe"""
    if os.path.exists(file_path):
        print(f"✅ {description}: {file_path}")
        return True
    else:
        print(f"❌ {description}: {file_path} - MANQUANT")
        return False

def check_directory_exists(dir_path, description):
    """Vérifie si un répertoire existe"""
    if os.path.exists(dir_path) and os.path.isdir(dir_path):
        print(f"✅ {description}: {dir_path}")
        return True
    else:
        print(f"❌ {description}: {dir_path} - MANQUANT")
        return False

def check_content_in_file(file_path, content, description):
    """Vérifie si un contenu spécifique existe dans un fichier"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            file_content = f.read()
            if content in file_content:
                print(f"✅ {description}")
                return True
            else:
                print(f"❌ {description} - CONTENU MANQUANT")
                return False
    except FileNotFoundError:
        print(f"❌ {description} - FICHIER MANQUANT: {file_path}")
        return False

def main():
    print("🔍 Vérification de la complétude du système de salles et tables")
    print("=" * 70)
    
    all_good = True
    
    # 1. Vérification des modèles
    print("\n📊 1. Modèles de données")
    files_to_check = [
        ("app/modules/tables/models_table.py", "Modèles Table et Room"),
        ("app/modules/pos/models_sale.py", "Modèle Sale étendu"),
    ]
    
    for file_path, description in files_to_check:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # Vérifier le contenu des modèles
    model_checks = [
        ("app/modules/tables/models_table.py", "class Room", "Modèle Room"),
        ("app/modules/tables/models_table.py", "position_x", "Colonnes de position"),
        ("app/modules/tables/models_table.py", "table_shape", "Colonnes d'apparence"),
        ("app/modules/tables/models_table.py", "current_covers", "Colonne couverts"),
        ("app/modules/pos/models_sale.py", "covers_count", "Colonne couverts dans Sale"),
        ("app/modules/pos/models_sale.py", "service_type", "Colonne type de service"),
    ]
    
    for file_path, content, description in model_checks:
        if not check_content_in_file(file_path, content, description):
            all_good = False
    
    # 2. Vérification du module rooms
    print("\n🏢 2. Module Rooms")
    rooms_files = [
        ("app/modules/rooms/__init__.py", "Blueprint rooms"),
        ("app/modules/rooms/routes.py", "Routes rooms"),
        ("app/modules/rooms/forms_room.py", "Formulaires rooms"),
        ("app/modules/rooms/templates/rooms/index.html", "Template index rooms"),
        ("app/modules/rooms/templates/rooms/form.html", "Template formulaire rooms"),
        ("app/modules/rooms/templates/rooms/layout.html", "Template plan de salle"),
    ]
    
    for file_path, description in rooms_files:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # 3. Vérification des templates POS
    print("\n💰 3. Templates POS étendus")
    pos_templates = [
        ("app/modules/pos/templates/pos/service_selector.html", "Sélecteur de service"),
        ("app/modules/pos/templates/pos/index.html", "POS principal modifié"),
        ("app/modules/pos/templates/pos/sale_details.html", "Détails de vente modifiés"),
    ]
    
    for file_path, description in pos_templates:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # 4. Vérification des scripts JavaScript
    print("\n📜 4. Scripts JavaScript")
    js_files = [
        ("app/modules/pos/static/js/service-selector.js", "Sélecteur de service JS"),
        ("app/modules/pos/static/js/pos.js", "POS principal JS"),
        ("app/modules/pos/static/js/cart-ui.js", "Interface panier JS"),
    ]
    
    for file_path, description in js_files:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # Vérifier le contenu JavaScript
    js_checks = [
        ("app/modules/pos/static/js/service-selector.js", "class ServiceSelector", "Classe ServiceSelector"),
        ("app/modules/pos/static/js/pos.js", "covers_count", "Gestion des couverts"),
        ("app/modules/pos/static/js/pos.js", "service_type", "Gestion du type de service"),
    ]
    
    for file_path, content, description in js_checks:
        if not check_content_in_file(file_path, content, description):
            all_good = False
    
    # 5. Vérification des formulaires
    print("\n📝 5. Formulaires")
    form_checks = [
        ("app/modules/tables/forms_table.py", "room_id", "Champ salle dans TableForm"),
        ("app/modules/tables/forms_table.py", "table_shape", "Champs apparence"),
        ("app/modules/rooms/forms_room.py", "class RoomForm", "Formulaire Room"),
    ]
    
    for file_path, content, description in form_checks:
        if not check_content_in_file(file_path, content, description):
            all_good = False
    
    # 6. Vérification des routes
    print("\n🛣️ 6. Routes")
    route_checks = [
        ("app/modules/rooms/routes.py", "@bp.route('/')", "Routes rooms"),
        ("app/modules/pos/routes.py", "get_rooms", "API get_rooms"),
        ("app/modules/tables/routes.py", "room_id", "Gestion room_id dans tables"),
    ]
    
    for file_path, content, description in route_checks:
        if not check_content_in_file(file_path, content, description):
            all_good = False
    
    # 7. Vérification de l'intégration
    print("\n🔗 7. Intégration")
    integration_checks = [
        ("app/__init__.py", "rooms_bp", "Blueprint rooms enregistré"),
        ("app/templates/navbar.html", "rooms.index", "Lien salles dans navbar"),
        ("app/modules/tables/templates/tables/form.html", "room_id", "Sélecteur de salle dans formulaire table"),
        ("app/modules/tables/templates/tables/index.html", "room.name", "Affichage salle dans liste tables"),
    ]
    
    for file_path, content, description in integration_checks:
        if not check_content_in_file(file_path, content, description):
            all_good = False
    
    # 8. Vérification des scripts d'installation
    print("\n⚙️ 8. Scripts d'installation et test")
    install_files = [
        ("init_rooms_and_tables.py", "Script d'initialisation"),
        ("test_rooms_system.py", "Script de test"),
        ("ROOMS_AND_TABLES_GUIDE.md", "Guide d'utilisation"),
    ]
    
    for file_path, description in install_files:
        if not check_file_exists(file_path, description):
            all_good = False
    
    # Résumé
    print("\n" + "=" * 70)
    if all_good:
        print("🎉 SYSTÈME COMPLET - Tous les éléments sont en place!")
        print("\n📋 Prochaines étapes:")
        print("1. Exécuter: python init_rooms_and_tables.py")
        print("2. Tester: python test_rooms_system.py")
        print("3. Accéder à /rooms/ pour gérer les salles")
        print("4. Utiliser le POS avec le nouveau sélecteur de service")
        return 0
    else:
        print("⚠️ SYSTÈME INCOMPLET - Certains éléments manquent!")
        print("\n🔧 Actions requises:")
        print("- Vérifiez les fichiers manquants listés ci-dessus")
        print("- Assurez-vous que tous les imports sont corrects")
        print("- Relancez ce script après correction")
        return 1

if __name__ == "__main__":
    sys.exit(main())
