{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h2 class="mb-0">{{ title }}</h2>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.csrf_token }}
                        
                        <!-- Salle et informations de base -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.room_id.label(class="form-label") }}
                                    {{ form.room_id(class="form-select") }}
                                    {% if form.room_id.errors %}
                                        {% for error in form.room_id.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                    <div class="form-text">Sélectionnez la salle où placer cette table</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.number.label(class="form-label") }}
                                    {{ form.number(class="form-control") }}
                                    {% if form.number.errors %}
                                        {% for error in form.number.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.capacity.label(class="form-label") }}
                                    {{ form.capacity(class="form-control") }}
                                    {% if form.capacity.errors %}
                                        {% for error in form.capacity.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Apparence de la table -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.table_shape.label(class="form-label") }}
                                    {{ form.table_shape(class="form-select") }}
                                    {% if form.table_shape.errors %}
                                        {% for error in form.table_shape.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.table_size.label(class="form-label") }}
                                    {{ form.table_size(class="form-select") }}
                                    {% if form.table_size.errors %}
                                        {% for error in form.table_size.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.table_color.label(class="form-label") }}
                                    {{ form.table_color(class="form-control", type="color") }}
                                    {% if form.table_color.errors %}
                                        {% for error in form.table_color.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    {{ form.location.label(class="form-label") }}
                                    {{ form.location(class="form-select") }}
                                    {% if form.location.errors %}
                                        {% for error in form.location.errors %}
                                            <span class="text-danger">{{ error }}</span>
                                        {% endfor %}
                                    {% endif %}
                                    <div class="form-text">Optionnel</div>
                                </div>
                            </div>
                        </div>

                        <!-- Position (cachée, sera définie par le plan de salle) -->
                        {{ form.position_x() }}
                        {{ form.position_y() }}

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('tables.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Enregistrer
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 