from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, IntegerField, SelectField, DateTimeField, TextAreaField, HiddenField
from wtforms.validators import DataRequired, Optional, NumberRange, Length
from flask_login import current_user

class TableForm(FlaskForm):
    number = StringField('Numéro', validators=[DataRequired(), Length(max=10)])
    capacity = IntegerField('Capacité', validators=[DataRequired(), NumberRange(min=1)])
    room_id = SelectField('Salle', coerce=int, validators=[DataRequired()])
    location = SelectField('Emplacement',
        choices=[
            ('interior', 'Intérieur'),
            ('terrace', 'Terrasse'),
            ('bar', 'Bar'),
            ('private', 'Salle privée')
        ],
        validators=[Optional()]
    )

    # Apparence de la table
    table_shape = SelectField('Forme',
        choices=[
            ('round', 'Ronde'),
            ('square', 'Carrée'),
            ('rectangle', 'Rectangulaire')
        ],
        default='round'
    )

    table_size = SelectField('Taille',
        choices=[
            ('small', 'Petite'),
            ('medium', 'Moyenne'),
            ('large', 'Grande')
        ],
        default='medium'
    )

    table_color = StringField('Couleur', default='#8B4513')

    # Position (sera remplie par JavaScript)
    position_x = HiddenField(default=100)
    position_y = HiddenField(default=100)

    def __init__(self, *args, **kwargs):
        super(TableForm, self).__init__(*args, **kwargs)
        # Charger les salles disponibles
        if current_user.is_authenticated:
            from app.modules.tables.models_table import Room
            rooms = Room.query.filter_by(owner_id=current_user.get_owner_id, is_active=True).all()
            self.room_id.choices = [(room.id, room.name) for room in rooms]

class TableReservationForm(FlaskForm):
    customer_name = StringField('Nom du client', validators=[DataRequired(), Length(max=100)])
    customer_phone = StringField('Téléphone', validators=[DataRequired(), Length(max=20)])
    number_of_guests = IntegerField('Nombre de personnes', validators=[DataRequired(), NumberRange(min=1)])
    reservation_date = DateTimeField('Date et heure', format='%Y-%m-%d %H:%M', validators=[DataRequired()])
    duration_minutes = IntegerField('Durée (minutes)', 
        validators=[DataRequired(), NumberRange(min=30, max=480)],
        default=120
    )
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)]) 