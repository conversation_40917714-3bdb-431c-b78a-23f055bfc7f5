"""Add rooms and table enhancements

Revision ID: add_rooms_tables
Revises: 
Create Date: 2024-01-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = 'add_rooms_tables'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Créer la table rooms
    op.create_table('rooms',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('owner_id', sa.Integer(), nullable=False),
        sa.<PERSON>umn('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('width', sa.Integer(), nullable=True),
        sa.Column('height', sa.Integer(), nullable=True),
        sa.<PERSON>umn('background_color', sa.String(length=7), nullable=True),
        sa.Column('is_default', sa.<PERSON>(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=True),
        sa.<PERSON>umn('created_at', sa.DateTime(), nullable=True),
        sa.<PERSON>umn('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['owner_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('owner_id', 'name', name='uq_owner_room_name')
    )

    # Ajouter les nouvelles colonnes à la table tables
    with op.batch_alter_table('tables', schema=None) as batch_op:
        # Ajouter room_id (nullable au début pour la migration)
        batch_op.add_column(sa.Column('room_id', sa.Integer(), nullable=True))
        
        # Ajouter les colonnes pour les couverts
        batch_op.add_column(sa.Column('current_covers', sa.Integer(), nullable=True))
        
        # Ajouter les colonnes pour la position et l'apparence
        batch_op.add_column(sa.Column('position_x', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('position_y', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('table_shape', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('table_size', sa.String(length=20), nullable=True))
        batch_op.add_column(sa.Column('table_color', sa.String(length=7), nullable=True))
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), nullable=True))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), nullable=True))
        
        # Ajouter la clé étrangère vers rooms
        batch_op.create_foreign_key('fk_tables_room', 'rooms', ['room_id'], ['id'])

    # Ajouter les nouvelles colonnes à la table sales
    with op.batch_alter_table('sales', schema=None) as batch_op:
        batch_op.add_column(sa.Column('covers_count', sa.Integer(), nullable=True))
        batch_op.add_column(sa.Column('service_type', sa.String(length=20), nullable=True))

    # Créer une salle par défaut pour chaque utilisateur existant
    connection = op.get_bind()
    
    # Récupérer tous les utilisateurs
    users_result = connection.execute(sa.text("SELECT id FROM users"))
    users = users_result.fetchall()
    
    for user in users:
        user_id = user[0]
        
        # Créer une salle par défaut
        connection.execute(sa.text("""
            INSERT INTO rooms (owner_id, name, description, width, height, background_color, is_default, is_active, created_at, updated_at)
            VALUES (:owner_id, 'Salle principale', 'Salle par défaut', 800, 600, '#f8f9fa', 1, 1, datetime('now'), datetime('now'))
        """), {'owner_id': user_id})
        
        # Récupérer l'ID de la salle créée
        room_result = connection.execute(sa.text("""
            SELECT id FROM rooms WHERE owner_id = :owner_id AND is_default = 1
        """), {'owner_id': user_id})
        room = room_result.fetchone()
        
        if room:
            room_id = room[0]
            
            # Mettre à jour toutes les tables de cet utilisateur
            connection.execute(sa.text("""
                UPDATE tables 
                SET room_id = :room_id,
                    current_covers = 0,
                    position_x = 100,
                    position_y = 100,
                    table_shape = 'round',
                    table_size = 'medium',
                    table_color = '#8B4513',
                    created_at = datetime('now'),
                    updated_at = datetime('now')
                WHERE owner_id = :owner_id
            """), {'room_id': room_id, 'owner_id': user_id})

    # Mettre à jour les ventes existantes
    connection.execute(sa.text("""
        UPDATE sales 
        SET covers_count = 1,
            service_type = 'dine_in'
        WHERE covers_count IS NULL
    """))

    # Maintenant rendre room_id non nullable
    with op.batch_alter_table('tables', schema=None) as batch_op:
        batch_op.alter_column('room_id', nullable=False)
        batch_op.alter_column('current_covers', nullable=False)
        batch_op.alter_column('position_x', nullable=False)
        batch_op.alter_column('position_y', nullable=False)
        batch_op.alter_column('table_shape', nullable=False)
        batch_op.alter_column('table_size', nullable=False)
        batch_op.alter_column('table_color', nullable=False)

    # Mettre à jour les contraintes de la table tables
    with op.batch_alter_table('tables', schema=None) as batch_op:
        # Supprimer l'ancienne contrainte unique
        batch_op.drop_constraint('uq_owner_table_number', type_='unique')
        # Créer la nouvelle contrainte unique
        batch_op.create_unique_constraint('uq_owner_room_table_number', ['owner_id', 'room_id', 'number'])

def downgrade():
    # Supprimer les contraintes et colonnes ajoutées
    with op.batch_alter_table('tables', schema=None) as batch_op:
        batch_op.drop_constraint('uq_owner_room_table_number', type_='unique')
        batch_op.create_unique_constraint('uq_owner_table_number', ['owner_id', 'number'])
        batch_op.drop_constraint('fk_tables_room', type_='foreignkey')
        batch_op.drop_column('updated_at')
        batch_op.drop_column('created_at')
        batch_op.drop_column('table_color')
        batch_op.drop_column('table_size')
        batch_op.drop_column('table_shape')
        batch_op.drop_column('position_y')
        batch_op.drop_column('position_x')
        batch_op.drop_column('current_covers')
        batch_op.drop_column('room_id')

    with op.batch_alter_table('sales', schema=None) as batch_op:
        batch_op.drop_column('service_type')
        batch_op.drop_column('covers_count')

    # Supprimer la table rooms
    op.drop_table('rooms')
