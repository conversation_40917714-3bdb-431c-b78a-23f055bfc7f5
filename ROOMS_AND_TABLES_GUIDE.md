# 🏢 Guide du Système de Gestion des Salles et Tables

## 📋 Vue d'ensemble

Ce système complet de gestion des salles et tables pour votre POS inclut :

- **Gestion des salles** : Créer et organiser plusieurs salles de restaurant
- **Plans de salle interactifs** : Positionnement des tables par glisser-déposer
- **Sélection de service** : Choix du type de service (sur place, à emporter, etc.)
- **Gestion des couverts** : Suivi du nombre de personnes par table
- **Paiements partiels** : Gestion des paiements par personne
- **Historique complet** : Suivi des commandes et paiements

## 🚀 Installation et Configuration

### 1. Initialisation de la base de données

Exécutez le script d'initialisation pour créer les nouvelles tables et colonnes :

```bash
python init_rooms_and_tables.py
```

Ce script va :
- Créer les nouvelles tables (rooms) et colonnes
- Migrer vos tables existantes vers une salle par défaut
- Ajouter des tables d'exemple si vous n'en avez pas
- Mettre à jour les ventes existantes avec les nouvelles colonnes

### 2. Vérification de l'installation

Après l'exécution du script, vous devriez voir :
- ✅ Tables créées
- ✅ Salle par défaut créée pour chaque utilisateur
- ✅ Tables migrées vers la salle par défaut
- ✅ Ventes mises à jour

## 🏗️ Fonctionnalités Principales

### 1. Gestion des Salles (`/rooms/`)

#### Créer une nouvelle salle
- Accédez à `/rooms/` et cliquez sur "Nouvelle Salle"
- Configurez :
  - **Nom** : Nom de la salle (ex: "Terrasse", "Salle principale")
  - **Description** : Description optionnelle
  - **Dimensions** : Largeur et hauteur en pixels pour le plan
  - **Couleur de fond** : Couleur d'arrière-plan du plan
  - **Salle par défaut** : Salle sélectionnée automatiquement
  - **Statut** : Actif/Inactif

#### Gérer le plan de salle
- Cliquez sur "Plan de salle" pour une salle
- **Mode édition** : Activez pour déplacer les tables
- **Glisser-déposer** : Repositionnez les tables en temps réel
- **Ajouter des tables** : Formulaire dans le panneau latéral
- **Propriétés des tables** : Cliquez sur une table pour modifier ses propriétés

### 2. Nouveau Processus de Commande dans le POS

#### Étape 1 : Sélection du type de service
Cliquez sur "Nouvelle commande" dans le POS pour ouvrir le sélecteur :

- **🪑 Sur place** : Service à table dans le restaurant
- **🛍️ À emporter** : Commande à emporter
- **🏍️ Livraison** : Livraison à domicile
- **🚗 Service au volant** : Commande depuis le véhicule

#### Étape 2 : Sélection de table (pour "Sur place")
- Choisissez la salle dans la liste
- Visualisez le plan de la salle en temps réel
- Cliquez sur une table disponible (verte)
- Les tables occupées (rouges) ne sont pas sélectionnables

#### Étape 3 : Nombre de couverts
- Sélectionnez le nombre de personnes (1-8)
- Ou saisissez un nombre personnalisé
- Cette information est utilisée pour les statistiques et la facturation

#### Étape 4 : Commande normale
- Ajoutez des produits au panier
- Envoyez en cuisine ou procédez au paiement
- Les informations de service sont automatiquement incluses

### 3. Indicateurs Visuels

#### États des tables
- 🟢 **Vert** : Table disponible
- 🔴 **Rouge** : Table occupée
- 🟡 **Jaune** : Table réservée
- ⚫ **Gris** : Table en nettoyage

#### Informations affichées sur les tables
- **Numéro de table** : Au centre
- **👥 Nombre de couverts** : Badge bleu en haut à gauche
- **💰 Montant** : Badge vert en bas à droite

### 4. Gestion des Paiements Partiels

#### Fonctionnalités
- **Paiement par personne** : Diviser l'addition
- **Paiements multiples** : Plusieurs méthodes de paiement
- **Suivi en temps réel** : Montant payé vs restant
- **Historique complet** : Tous les paiements enregistrés

#### Utilisation
1. Créez une commande avec plusieurs couverts
2. Dans les détails de vente, vous verrez :
   - Total de la commande
   - Montant déjà payé
   - Montant restant
   - Historique des paiements

## 🎨 Personnalisation

### Apparence des tables
- **Formes** : Ronde, carrée, rectangulaire
- **Tailles** : Petite, moyenne, grande
- **Couleurs** : Personnalisables via un sélecteur de couleur

### Plans de salle
- **Dimensions** : Configurables (400-2000px de largeur, 300-1500px de hauteur)
- **Couleur de fond** : Personnalisable
- **Grille** : Affichage automatique pour l'alignement

## 📊 Rapports et Statistiques

### Nouvelles données disponibles
- **Couverts par service** : Nombre moyen de personnes
- **Répartition par type de service** : Sur place vs à emporter vs livraison
- **Utilisation des tables** : Tables les plus/moins utilisées
- **Temps d'occupation** : Durée moyenne par table

### Accès aux données
Les nouvelles informations sont disponibles dans :
- Détails des ventes (`/pos/sales/<id>`)
- Rapports de ventes (`/pos/sales/stats`)
- Historique des tables

## 🔧 API et Intégrations

### Nouvelles routes API
- `GET /pos/api/get_rooms` : Liste des salles disponibles
- `GET /rooms/api/get_room_data/<id>` : Données d'une salle avec tables
- `POST /rooms/api/update_table_position` : Mise à jour position table
- `POST /rooms/api/update_table_appearance` : Mise à jour apparence table

### Données JavaScript
Le système utilise `sessionStorage` pour conserver :
- Type de service sélectionné
- Table choisie
- Nombre de couverts
- Informations de la salle

## 🚨 Dépannage

### Problèmes courants

#### "Aucune salle configurée"
- Exécutez `python init_rooms_and_tables.py`
- Ou créez manuellement une salle via `/rooms/new`

#### "Tables non visibles sur le plan"
- Vérifiez que les tables ont une `room_id` assignée
- Vérifiez les positions `position_x` et `position_y`
- Assurez-vous que la salle est active

#### "Erreur lors du glisser-déposer"
- Vérifiez que le mode édition est activé
- Assurez-vous d'avoir les permissions `can_manage_tables`
- Vérifiez la console JavaScript pour les erreurs

### Logs et débogage
- Les erreurs JavaScript sont loggées dans la console du navigateur
- Les erreurs serveur sont dans les logs Flask
- Utilisez `console.log` dans `service-selector.js` pour déboguer

## 📱 Compatibilité

### Navigateurs supportés
- Chrome/Chromium 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Appareils
- **Desktop** : Expérience complète
- **Tablettes** : Interface adaptée pour le glisser-déposer
- **Mobiles** : Interface simplifiée (sélection par liste)

## 🔄 Mises à jour futures

### Fonctionnalités prévues
- **Réservations** : Système de réservation de tables
- **QR Codes** : Commande directe depuis la table
- **Notifications** : Alertes en temps réel
- **Rapports avancés** : Analytics détaillées

### Migration
Le système est conçu pour être rétrocompatible. Les futures mises à jour incluront des scripts de migration automatique.

---

## 📞 Support

Pour toute question ou problème :
1. Vérifiez ce guide
2. Consultez les logs d'erreur
3. Testez avec le script d'initialisation
4. Contactez le support technique

**Bon usage de votre nouveau système de gestion des salles et tables ! 🎉**
