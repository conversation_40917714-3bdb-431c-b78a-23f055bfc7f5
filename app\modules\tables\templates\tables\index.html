{% extends "base.html" %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-chair"></i> Gestion des Tables</h1>
        <div>
            <a href="{{ url_for('rooms.index') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-door-open"></i> Salles
            </a>
            <a href="{{ url_for('tables.reservations') }}" class="btn btn-info me-2">
                <i class="fas fa-calendar-alt"></i> Réservations
            </a>
            <a href="{{ url_for('tables.new') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Nouvelle Table
            </a>
        </div>
    </div>

    <!-- Filtre par salle -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" class="row g-3 align-items-end">
                <div class="col-md-4">
                    <label for="room_id" class="form-label">Filtrer par salle</label>
                    <select name="room_id" id="room_id" class="form-select" onchange="this.form.submit()">
                        <option value="">Toutes les salles</option>
                        {% for room in rooms %}
                        <option value="{{ room.id }}" {% if selected_room_id == room.id %}selected{% endif %}>
                            {{ room.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        {% for table in tables %}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ table.display_name }}</h5>
                        <span class="badge {% if table.status == 'available' %}bg-success{% elif table.status == 'occupied' %}bg-danger{% elif table.status == 'reserved' %}bg-warning{% else %}bg-secondary{% endif %}">
                            {% if table.status == 'available' %}
                                Disponible
                            {% elif table.status == 'occupied' %}
                                Occupée
                            {% elif table.status == 'reserved' %}
                                Réservée
                            {% else %}
                                Nettoyage
                            {% endif %}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        {% if table.room %}
                        <strong>Salle:</strong> {{ table.room.name }}<br>
                        {% endif %}
                        <strong>Capacité:</strong> {{ table.capacity }} personnes<br>
                        {% if table.current_covers > 0 %}
                        <strong>Couverts actuels:</strong> {{ table.current_covers }}<br>
                        {% endif %}
                        {% if table.location %}
                        <strong>Emplacement:</strong>
                        {% if table.location == 'interior' %}
                            Intérieur
                        {% elif table.location == 'terrace' %}
                            Terrasse
                        {% elif table.location == 'bar' %}
                            Bar
                        {% else %}
                            Salle privée
                        {% endif %}<br>
                        {% endif %}
                        <strong>Forme:</strong>
                        {% if table.table_shape == 'round' %}
                            Ronde
                        {% elif table.table_shape == 'square' %}
                            Carrée
                        {% else %}
                            Rectangulaire
                        {% endif %}
                        ({{ table.table_size }})
                    </div>

                    {% if table.current_order_id %}
                    <div class="alert alert-info mb-3">
                        <strong>Commande en cours:</strong> #{{ table.current_order_id }}<br>
                        {% if table.current_amount > 0 %}
                        <strong>Montant:</strong> {{ "%.2f"|format(table.current_amount) }} €<br>
                        {% endif %}
                        {% if table.remaining_amount > 0 %}
                        <strong>Restant à payer:</strong> {{ "%.2f"|format(table.remaining_amount) }} €
                        {% endif %}
                    </div>
                    {% endif %}

                    {% if table.reservations %}
                    <div class="mb-3">
                        <strong>Prochaine réservation:</strong><br>
                        {% set next_reservation = table.reservations|selectattr('is_active')|first %}
                        {% if next_reservation %}
                            {{ next_reservation.customer_name }}<br>
                            {{ next_reservation.reservation_date.strftime('%d/%m/%Y %H:%M') }}
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        {% if table.room %}
                        <a href="{{ url_for('rooms.show', id=table.room.id) }}" class="btn btn-outline-primary btn-sm" title="Plan de salle">
                            <i class="fas fa-map"></i>
                        </a>
                        {% endif %}
                        <a href="{{ url_for('tables.show', id=table.id) }}" class="btn btn-info btn-sm" title="Voir">
                            <i class="fas fa-eye"></i>
                        </a>
                        <a href="{{ url_for('tables.edit', id=table.id) }}" class="btn btn-warning btn-sm" title="Modifier">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" class="btn btn-danger btn-sm" onclick="confirmDelete({{ table.id }})" title="Supprimer">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Modal de confirmation de suppression -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirmer la suppression</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                Êtes-vous sûr de vouloir supprimer cette table ?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Supprimer</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function confirmDelete(tableId) {
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    const form = document.getElementById('deleteForm');
    form.action = `/tables/${tableId}/delete`;
    // Met à jour le token CSRF depuis la meta
    const csrfInput = form.querySelector('input[name=csrf_token]');
    const metaToken = document.querySelector('meta[name=csrf-token]');
    if (csrfInput && metaToken) {
        csrfInput.value = metaToken.getAttribute('content');
    }
    modal.show();
}
</script>
{% endblock %} 