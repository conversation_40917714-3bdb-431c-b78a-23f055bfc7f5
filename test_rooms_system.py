#!/usr/bin/env python3
"""
Script de test pour vérifier le bon fonctionnement du système de salles et tables
"""

import os
import sys
from datetime import datetime

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.modules.auth.models import User
from app.modules.tables.models_table import Room, Table, TableStatus
from app.modules.pos.models_sale import Sale

def test_rooms_system():
    """Test complet du système de salles et tables"""
    app = create_app()
    
    with app.app_context():
        print("🧪 Test du système de gestion des salles et tables")
        print("=" * 60)
        
        # Test 1: Vérifier les modèles
        print("\n1️⃣ Test des modèles de données...")
        
        try:
            # Compter les salles
            rooms_count = Room.query.count()
            tables_count = Table.query.count()
            users_count = User.query.count()
            
            print(f"   📊 {users_count} utilisateur(s)")
            print(f"   🏢 {rooms_count} salle(s)")
            print(f"   🪑 {tables_count} table(s)")
            
            if rooms_count == 0:
                print("   ⚠️  Aucune salle trouvée. Exécutez init_rooms_and_tables.py")
                return False
                
        except Exception as e:
            print(f"   ❌ Erreur lors du test des modèles: {str(e)}")
            return False
        
        # Test 2: Vérifier les relations
        print("\n2️⃣ Test des relations entre modèles...")
        
        try:
            for room in Room.query.limit(3).all():
                print(f"   🏢 Salle '{room.name}':")
                print(f"      - {room.table_count} table(s)")
                print(f"      - {room.available_tables_count} disponible(s)")
                print(f"      - {room.occupied_tables_count} occupée(s)")
                
                # Tester les propriétés des tables
                for table in room.tables[:2]:  # Limiter à 2 tables par salle
                    print(f"      🪑 Table {table.number}:")
                    print(f"         - Capacité: {table.capacity}")
                    print(f"         - Position: ({table.position_x}, {table.position_y})")
                    print(f"         - Forme: {table.table_shape}")
                    print(f"         - Statut: {table.status}")
                    print(f"         - Nom d'affichage: {table.display_name}")
                    
        except Exception as e:
            print(f"   ❌ Erreur lors du test des relations: {str(e)}")
            return False
        
        # Test 3: Tester les méthodes des tables
        print("\n3️⃣ Test des méthodes des tables...")
        
        try:
            # Prendre une table disponible
            available_table = Table.query.filter_by(status=TableStatus.AVAILABLE).first()
            
            if available_table:
                print(f"   🪑 Test avec la table {available_table.number}")
                
                # Test d'occupation
                print("      - Test d'occupation...")
                original_status = available_table.status
                available_table.occupy(999, 4)  # ID fictif, 4 couverts
                
                if available_table.status == TableStatus.OCCUPIED:
                    print("      ✅ Occupation réussie")
                    print(f"         - Couverts: {available_table.current_covers}")
                    print(f"         - Commande: {available_table.current_order_id}")
                else:
                    print("      ❌ Échec de l'occupation")
                
                # Test de libération
                print("      - Test de libération...")
                available_table.reset_table()
                
                if available_table.status == TableStatus.AVAILABLE:
                    print("      ✅ Libération réussie")
                    print(f"         - Couverts: {available_table.current_covers}")
                    print(f"         - Commande: {available_table.current_order_id}")
                else:
                    print("      ❌ Échec de la libération")
                
                # Test de mise à jour de position
                print("      - Test de mise à jour de position...")
                old_x, old_y = available_table.position_x, available_table.position_y
                available_table.update_position(200, 300)
                
                if available_table.position_x == 200 and available_table.position_y == 300:
                    print("      ✅ Mise à jour de position réussie")
                    # Remettre l'ancienne position
                    available_table.update_position(old_x, old_y)
                else:
                    print("      ❌ Échec de la mise à jour de position")
                
            else:
                print("   ⚠️  Aucune table disponible pour les tests")
                
        except Exception as e:
            print(f"   ❌ Erreur lors du test des méthodes: {str(e)}")
            return False
        
        # Test 4: Tester les ventes avec couverts
        print("\n4️⃣ Test des ventes avec couverts...")
        
        try:
            # Vérifier les ventes existantes
            sales_with_covers = Sale.query.filter(Sale.covers_count.isnot(None)).limit(3).all()
            
            if sales_with_covers:
                for sale in sales_with_covers:
                    print(f"   💰 Vente #{sale.id}:")
                    print(f"      - Couverts: {sale.covers_count}")
                    print(f"      - Service: {sale.service_type_display}")
                    print(f"      - Total: {sale.total}€")
                    print(f"      - Payé: {sale.total_paid}€")
                    print(f"      - Restant: {sale.remaining_amount}€")
                    
                    if sale.table:
                        print(f"      - Table: {sale.table.display_name}")
            else:
                print("   ℹ️  Aucune vente avec couverts trouvée")
                
        except Exception as e:
            print(f"   ❌ Erreur lors du test des ventes: {str(e)}")
            return False
        
        # Test 5: Vérifier les contraintes
        print("\n5️⃣ Test des contraintes de base de données...")
        
        try:
            # Test de contrainte unique sur les salles
            user = User.query.first()
            if user:
                existing_room = Room.query.filter_by(owner_id=user.id).first()
                if existing_room:
                    try:
                        duplicate_room = Room(
                            owner_id=user.id,
                            name=existing_room.name,  # Même nom
                            width=800,
                            height=600
                        )
                        db.session.add(duplicate_room)
                        db.session.commit()
                        print("   ❌ Contrainte unique sur les salles non respectée")
                        return False
                    except Exception:
                        db.session.rollback()
                        print("   ✅ Contrainte unique sur les salles respectée")
                
        except Exception as e:
            print(f"   ❌ Erreur lors du test des contraintes: {str(e)}")
            return False
        
        # Test 6: Vérifier les routes (simulation)
        print("\n6️⃣ Test de la structure des routes...")
        
        try:
            from app.modules.rooms.routes import bp as rooms_bp
            from app.modules.pos.routes import get_rooms
            
            print("   ✅ Blueprint rooms importé")
            print("   ✅ Route get_rooms importée")
            print("   ✅ Structure des routes OK")
            
        except Exception as e:
            print(f"   ❌ Erreur lors du test des routes: {str(e)}")
            return False
        
        print("\n🎉 Tous les tests sont passés avec succès!")
        print("\n📋 Résumé du système:")
        print(f"   - {users_count} utilisateur(s) configuré(s)")
        print(f"   - {rooms_count} salle(s) disponible(s)")
        print(f"   - {tables_count} table(s) configurée(s)")
        print(f"   - Système de couverts: ✅")
        print(f"   - Gestion des positions: ✅")
        print(f"   - Relations entre modèles: ✅")
        print(f"   - Contraintes de base: ✅")
        
        print("\n🚀 Le système est prêt à être utilisé!")
        print("\nProchaines étapes:")
        print("1. Accédez à /rooms/ pour gérer vos salles")
        print("2. Utilisez le POS avec le nouveau sélecteur de service")
        print("3. Testez le glisser-déposer dans les plans de salle")
        
        return True

if __name__ == "__main__":
    try:
        success = test_rooms_system()
        if not success:
            print("\n💥 Certains tests ont échoué. Vérifiez la configuration.")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Erreur fatale lors des tests: {str(e)}")
        sys.exit(1)
