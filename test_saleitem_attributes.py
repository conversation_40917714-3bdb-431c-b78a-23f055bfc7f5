#!/usr/bin/env python3
"""
Script de test pour vérifier les attributs du modèle SaleItem
"""

import os
import sys

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_saleitem_model():
    """Test des attributs du modèle SaleItem"""
    try:
        from app.modules.pos.models_sale import SaleItem
        
        print("🧪 Test des attributs du modèle SaleItem")
        print("=" * 50)
        
        # Créer une instance fictive pour tester les attributs
        print("\n📋 Attributs disponibles dans SaleItem:")
        
        # Lister tous les attributs de la classe
        attributes = [attr for attr in dir(SaleItem) if not attr.startswith('_')]
        for attr in sorted(attributes):
            print(f"   - {attr}")
        
        # Vérifier les attributs spécifiques
        required_attrs = ['id', 'sale_id', 'product_id', 'quantity', 'price', 'total', 'created_at']
        print(f"\n✅ Attributs requis:")
        for attr in required_attrs:
            if hasattr(SaleItem, attr):
                print(f"   ✅ {attr}")
            else:
                print(f"   ❌ {attr} - MANQUANT")
        
        # Vérifier les relations
        relations = ['product', 'sale']
        print(f"\n🔗 Relations:")
        for rel in relations:
            if hasattr(SaleItem, rel):
                print(f"   ✅ {rel}")
            else:
                print(f"   ❌ {rel} - MANQUANT")
        
        print("\n🎯 Mapping des attributs pour l'API:")
        print("   - unit_price → price")
        print("   - total_price → total")
        print("   - notes → (non disponible dans le modèle)")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_sale_model():
    """Test des attributs du modèle Sale"""
    try:
        from app.modules.pos.models_sale import Sale
        
        print("\n🧪 Test des attributs du modèle Sale")
        print("=" * 50)
        
        # Vérifier les nouvelles colonnes
        new_attrs = ['covers_count', 'service_type']
        print(f"\n✅ Nouvelles colonnes:")
        for attr in new_attrs:
            if hasattr(Sale, attr):
                print(f"   ✅ {attr}")
            else:
                print(f"   ❌ {attr} - MANQUANT")
        
        # Vérifier les méthodes
        methods = ['calculate_totals', 'service_type_display', 'remaining_amount', 'is_partially_paid']
        print(f"\n🔧 Méthodes:")
        for method in methods:
            if hasattr(Sale, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - MANQUANT")
        
        return True
        
    except ImportError as e:
        print(f"❌ Erreur d'import: {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    print("🔍 Vérification des modèles SaleItem et Sale")
    print("=" * 60)
    
    success1 = test_saleitem_model()
    success2 = test_sale_model()
    
    if success1 and success2:
        print("\n🎉 Tous les tests sont passés!")
        print("\n📋 Résumé des corrections apportées:")
        print("   ✅ Relation 'product' ajoutée à SaleItem")
        print("   ✅ Mapping des attributs corrigé dans les APIs:")
        print("      - item.unit_price → item.price")
        print("      - item.total_price → item.total")
        print("      - item.notes → getattr(item, 'notes', '')")
        print("\n🚀 Le système devrait maintenant fonctionner correctement!")
        return 0
    else:
        print("\n⚠️ Certains tests ont échoué.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
