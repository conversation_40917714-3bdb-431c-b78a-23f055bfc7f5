from datetime import datetime, timedelta
from app import db

class TableStatus:
    AVAILABLE = 'available'
    OCCUPIED = 'occupied'
    RESERVED = 'reserved'
    CLEANING = 'cleaning'

class Room(db.Model):
    __tablename__ = 'rooms'
    __table_args__ = (
        db.UniqueConstraint('owner_id', 'name', name='uq_owner_room_name'),
        {'extend_existing': True}
    )

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    width = db.Column(db.Integer, default=800)  # Largeur en pixels pour le plan
    height = db.Column(db.Integer, default=600)  # Hauteur en pixels pour le plan
    background_color = db.Column(db.String(7), default='#f8f9fa')  # Couleur de fond
    is_default = db.Column(db.<PERSON>, default=False)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    owner = db.relationship('User', backref='rooms', lazy=True)
    tables = db.relationship('Table', backref='room', lazy=True, cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Room {self.name}>'

    @property
    def table_count(self):
        return len(self.tables)

    @property
    def occupied_tables_count(self):
        return len([t for t in self.tables if t.status == TableStatus.OCCUPIED])

    @property
    def available_tables_count(self):
        return len([t for t in self.tables if t.status == TableStatus.AVAILABLE])

class Table(db.Model):
    __tablename__ = 'tables'
    __table_args__ = (
        db.UniqueConstraint('owner_id', 'room_id', 'number', name='uq_owner_room_table_number'),
        {'extend_existing': True}
    )

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    room_id = db.Column(db.Integer, db.ForeignKey('rooms.id'), nullable=False)
    number = db.Column(db.String(10), nullable=False)
    capacity = db.Column(db.Integer, nullable=False)
    status = db.Column(db.String(20), default=TableStatus.AVAILABLE)
    location = db.Column(db.String(50), nullable=True)  # e.g., "Terrasse", "Intérieur", etc.
    current_order_id = db.Column(db.Integer, db.ForeignKey('sales.id'), nullable=True)
    current_covers = db.Column(db.Integer, default=0)  # Nombre de couverts actuels

    # Position et apparence sur le plan de la salle
    position_x = db.Column(db.Integer, default=100)
    position_y = db.Column(db.Integer, default=100)
    table_shape = db.Column(db.String(20), default='round')  # round, square, rectangle
    table_size = db.Column(db.String(20), default='medium')  # small, medium, large
    table_color = db.Column(db.String(7), default='#8B4513')  # Couleur de la table

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relations
    owner = db.relationship('User', backref='tables', lazy=True)
    reservations = db.relationship('TableReservation', backref='table', lazy=True)

    def __repr__(self):
        return f'<Table {self.number} - {self.room.name if self.room else "No Room"}>'

    @property
    def display_name(self):
        """Nom d'affichage de la table avec salle"""
        if self.room:
            return f"Table {self.number} ({self.room.name})"
        return f"Table {self.number}"

    @property
    def current_sale(self):
        """Retourne la vente en cours pour cette table"""
        if self.current_order_id:
            from app.modules.pos.models_sale import Sale
            return Sale.query.get(self.current_order_id)
        return None

    @property
    def current_amount(self):
        """Montant total de la commande en cours"""
        sale = self.current_sale
        return sale.total if sale else 0

    @property
    def remaining_amount(self):
        """Montant restant à payer"""
        sale = self.current_sale
        if sale:
            paid_amount = sum(p.amount for p in sale.payments)
            return max(0, sale.total - paid_amount)
        return 0

    def is_available(self):
        return self.status == TableStatus.AVAILABLE

    def occupy(self, order_id, covers_count=1, commit=True):
        """Occupe la table avec une commande"""
        if self.is_available():
            self.status = TableStatus.OCCUPIED
            self.current_order_id = order_id
            self.current_covers = covers_count
            if commit:
                db.session.commit()
            return True
        return False

    def add_covers(self, additional_covers, commit=True):
        """Ajoute des couverts à la table"""
        self.current_covers += additional_covers
        if commit:
            db.session.commit()

    def remove_covers(self, covers_to_remove, commit=True):
        """Retire des couverts de la table"""
        self.current_covers = max(0, self.current_covers - covers_to_remove)
        if self.current_covers == 0:
            self.reset_table(commit=False)
        if commit:
            db.session.commit()

    def update_position(self, x, y, commit=True):
        """Met à jour la position de la table sur le plan"""
        self.position_x = x
        self.position_y = y
        if commit:
            db.session.commit()

    def update_appearance(self, shape=None, size=None, color=None, commit=True):
        """Met à jour l'apparence de la table"""
        if shape:
            self.table_shape = shape
        if size:
            self.table_size = size
        if color:
            self.table_color = color
        if commit:
            db.session.commit()

    def release(self):
        self.status = TableStatus.CLEANING
        self.current_order_id = None
        self.current_covers = 0
        db.session.commit()

    def mark_clean(self):
        if self.status == TableStatus.CLEANING:
            self.status = TableStatus.AVAILABLE
            db.session.commit()
            return True
        return False

    def reset_table(self, commit=True):
        """Remet la table à l'état disponible après une commande"""
        self.status = TableStatus.AVAILABLE
        self.current_order_id = None
        self.current_covers = 0
        if commit:
            db.session.commit()

class TableReservation(db.Model):
    __tablename__ = 'table_reservations'

    id = db.Column(db.Integer, primary_key=True)
    table_id = db.Column(db.Integer, db.ForeignKey('tables.id'), nullable=False)
    customer_name = db.Column(db.String(100), nullable=False)
    customer_phone = db.Column(db.String(20), nullable=False)
    number_of_guests = db.Column(db.Integer, nullable=False)
    reservation_date = db.Column(db.DateTime, nullable=False)
    duration_minutes = db.Column(db.Integer, default=120)  # Durée par défaut 2h
    notes = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<Reservation {self.customer_name} - Table {self.table_id}>'

    def is_active(self):
        now = datetime.utcnow()
        return self.reservation_date <= now <= self.reservation_date + timedelta(minutes=self.duration_minutes)